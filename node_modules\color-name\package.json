{"_from": "color-name@~1.1.4", "_id": "color-name@1.1.4", "_inBundle": false, "_integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "_location": "/color-name", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "color-name@~1.1.4", "name": "color-name", "escapedName": "color-name", "rawSpec": "~1.1.4", "saveSpec": null, "fetchSpec": "~1.1.4"}, "_requiredBy": ["/color-convert"], "_resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "_shasum": "c2a09a87acbde69543de6f63fa3995c826c536a2", "_spec": "color-name@~1.1.4", "_where": "C:\\repos\\test\\node_modules\\color-convert", "author": {"name": "DY", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/colorjs/color-name/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A list of color names and its values", "files": ["index.js"], "homepage": "https://github.com/colorjs/color-name", "keywords": ["color-name", "color", "color-keyword", "keyword"], "license": "MIT", "main": "index.js", "name": "color-name", "repository": {"type": "git", "url": "git+ssh://**************/colorjs/color-name.git"}, "scripts": {"test": "node test.js"}, "version": "1.1.4"}