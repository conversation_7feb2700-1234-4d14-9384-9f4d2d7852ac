"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.QRWebviewProvider = void 0;
const vscode = require("vscode");
class QRWebviewProvider {
    constructor(extensionUri) {
        this.extensionUri = extensionUri;
    }
    async showQRCodes(qrCodes) {
        // Create or show the webview panel
        if (this.panel) {
            this.panel.reveal();
        }
        else {
            this.panel = vscode.window.createWebviewPanel('qrCodeViewer', 'QR Code Viewer', vscode.ViewColumn.One, {
                enableScripts: true,
                retainContextWhenHidden: true
            });
            this.panel.onDidDispose(() => {
                this.panel = undefined;
            });
        }
        // Set the HTML content
        this.panel.webview.html = this.getWebviewContent(qrCodes);
    }
    getWebviewContent(qrCodes) {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Viewer</title>
    <style>
        body {
            font-family: var(--vscode-font-family);
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .qr-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 40px;
            border: 1px solid var(--vscode-panel-border);
            border-radius: 8px;
            padding: 20px;
            background-color: var(--vscode-panel-background);
        }
        
        .qr-info {
            margin-bottom: 15px;
            text-align: center;
        }
        
        .chunk-number {
            font-size: 18px;
            font-weight: bold;
            color: var(--vscode-textLink-foreground);
        }
        
        .chunk-size {
            font-size: 14px;
            color: var(--vscode-descriptionForeground);
            margin-top: 5px;
        }
        
        .qr-image {
            max-width: 100%;
            height: auto;
            border: 2px solid var(--vscode-panel-border);
            border-radius: 4px;
            background-color: white;
            padding: 10px;
        }
        
        .content-preview {
            margin-top: 15px;
            max-width: 600px;
            width: 100%;
        }
        
        .content-preview details {
            background-color: var(--vscode-editor-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            padding: 10px;
        }
        
        .content-preview summary {
            cursor: pointer;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .content-preview pre {
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: var(--vscode-editor-font-family);
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            background-color: var(--vscode-textCodeBlock-background);
            padding: 10px;
            border-radius: 4px;
            margin: 0;
        }
        
        .navigation {
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: var(--vscode-panel-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            padding: 10px;
            font-size: 14px;
        }
        
        .stats {
            margin-top: 20px;
            padding: 15px;
            background-color: var(--vscode-panel-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="navigation">
        Total QR Codes: ${qrCodes.length}<br>
        Displayed in reverse order
    </div>
    
    <div class="header">
        <h1>QR Code Generator Results</h1>
        <p>Files processed and chunked into ${qrCodes.length} QR code${qrCodes.length !== 1 ? 's' : ''}</p>
    </div>
    
    ${qrCodes.map((qr, index) => `
        <div class="qr-container" id="qr-${qr.chunkIndex}">
            <div class="qr-info">
                <div class="chunk-number">Chunk ${qr.chunkIndex} of ${qr.totalChunks}</div>
                <div class="chunk-size">${qr.size} bytes</div>
            </div>
            
            <img src="${qr.dataUrl}" alt="QR Code ${qr.chunkIndex}" class="qr-image" />
            
            <div class="content-preview">
                <details>
                    <summary>View Content (${qr.size} bytes)</summary>
                    <pre>${this.escapeHtml(qr.content.substring(0, 1000))}${qr.content.length > 1000 ? '...' : ''}</pre>
                </details>
            </div>
        </div>
    `).join('')}
    
    <div class="stats">
        <h3>Processing Complete</h3>
        <p>Total chunks: ${qrCodes.length}</p>
        <p>Total size: ${qrCodes.reduce((sum, qr) => sum + qr.size, 0)} bytes</p>
        <p>QR codes are displayed in reverse order as requested</p>
    </div>
</body>
</html>`;
    }
    escapeHtml(text) {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }
}
exports.QRWebviewProvider = QRWebviewProvider;
//# sourceMappingURL=webview.js.map