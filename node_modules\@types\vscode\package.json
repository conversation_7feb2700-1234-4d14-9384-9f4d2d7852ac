{"_from": "@types/vscode@^1.74.0", "_id": "@types/vscode@1.100.0", "_inBundle": false, "_integrity": "sha512-4uNyvzHoraXEeCamR3+fzcBlh7Afs4Ifjs4epINyUX/jvdk0uzLnwiDY35UKDKnkCHP5Nu3dljl2H8lR6s+rQw==", "_location": "/@types/vscode", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/vscode@^1.74.0", "name": "@types/vscode", "escapedName": "@types%2fvscode", "scope": "@types", "rawSpec": "^1.74.0", "saveSpec": null, "fetchSpec": "^1.74.0"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/@types/vscode/-/vscode-1.100.0.tgz", "_shasum": "35cd628a86b11587856df94be94054aab01f2f17", "_spec": "@types/vscode@^1.74.0", "_where": "C:\\repos\\test", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "Visual Studio Code Team, Microsoft", "url": "https://github.com/microsoft"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for vscode", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/vscode", "license": "MIT", "main": "", "name": "@types/vscode", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/vscode"}, "scripts": {}, "typeScriptVersion": "5.1", "types": "index.d.ts", "typesPublisherContentHash": "c21cc220ed924e13193028cf11f7506bcff5a4a50153240b76aea48e553d925f", "version": "1.100.0"}