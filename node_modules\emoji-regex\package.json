{"_from": "emoji-regex@^8.0.0", "_id": "emoji-regex@8.0.0", "_inBundle": false, "_integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "_location": "/emoji-regex", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "emoji-regex@^8.0.0", "name": "emoji-regex", "escapedName": "emoji-regex", "rawSpec": "^8.0.0", "saveSpec": null, "fetchSpec": "^8.0.0"}, "_requiredBy": ["/string-width"], "_resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "_shasum": "e818fd69ce5ccfcb404594f842963bf53164cc37", "_spec": "emoji-regex@^8.0.0", "_where": "C:\\repos\\test\\node_modules\\string-width", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "bugs": {"url": "https://github.com/mathiasbynens/emoji-regex/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A regular expression to match all Emoji-only symbols as per the Unicode Standard.", "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.3.4", "@babel/plugin-proposal-unicode-property-regex": "^7.2.0", "@babel/preset-env": "^7.3.4", "mocha": "^6.0.2", "regexgen": "^1.3.0", "unicode-12.0.0": "^0.7.9"}, "files": ["LICENSE-MIT.txt", "index.js", "index.d.ts", "text.js", "es2015/index.js", "es2015/text.js"], "homepage": "https://mths.be/emoji-regex", "keywords": ["unicode", "regex", "regexp", "regular expressions", "code points", "symbols", "characters", "emoji"], "license": "MIT", "main": "index.js", "name": "emoji-regex", "repository": {"type": "git", "url": "git+https://github.com/mathiasbynens/emoji-regex.git"}, "scripts": {"build": "rm -rf -- es2015; babel src -d .; NODE_ENV=es2015 babel src -d ./es2015; node script/inject-sequences.js", "test": "mocha", "test:watch": "npm run test -- --watch"}, "types": "index.d.ts", "version": "8.0.0"}