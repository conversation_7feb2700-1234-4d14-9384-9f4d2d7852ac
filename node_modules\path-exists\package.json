{"_from": "path-exists@^4.0.0", "_id": "path-exists@4.0.0", "_inBundle": false, "_integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==", "_location": "/path-exists", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "path-exists@^4.0.0", "name": "path-exists", "escapedName": "path-exists", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/find-up"], "_resolved": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz", "_shasum": "513bdbe2d3b95d7762e8c1137efa195c6c61b5b3", "_spec": "path-exists@^4.0.0", "_where": "C:\\repos\\test\\node_modules\\find-up", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/path-exists/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Check if a path exists", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/path-exists#readme", "keywords": ["path", "exists", "exist", "file", "filepath", "fs", "filesystem", "file-system", "access", "stat"], "license": "MIT", "name": "path-exists", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/path-exists.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "4.0.0"}