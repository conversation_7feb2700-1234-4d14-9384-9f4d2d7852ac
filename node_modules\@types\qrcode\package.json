{"_from": "@types/qrcode@^1.5.2", "_id": "@types/qrcode@1.5.5", "_inBundle": false, "_integrity": "sha512-CdfBi/e3Qk+3Z/fXYShipBT13OJ2fDO2Q2w5CIP5anLTLIndQG9z6P1cnm+8zCWSpm5dnxMFd/uREtb0EXuQzg==", "_location": "/@types/qrcode", "_phantomChildren": {"undici-types": "6.21.0"}, "_requested": {"type": "range", "registry": true, "raw": "@types/qrcode@^1.5.2", "name": "@types/qrcode", "escapedName": "@types%2fqrcode", "scope": "@types", "rawSpec": "^1.5.2", "saveSpec": null, "fetchSpec": "^1.5.2"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/@types/qrcode/-/qrcode-1.5.5.tgz", "_shasum": "993ff7c6b584277eee7aac0a20861eab682f9dac", "_spec": "@types/qrcode@^1.5.2", "_where": "C:\\repos\\test", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "York Yao", "url": "https://github.com/plantain-00"}, {"name": "<PERSON>", "url": "https://github.com/mnahkies"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Marchelune"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}], "dependencies": {"@types/node": "*"}, "deprecated": false, "description": "TypeScript definitions for qrcode", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/qrcode", "license": "MIT", "main": "", "name": "@types/qrcode", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/qrcode"}, "scripts": {}, "typeScriptVersion": "4.5", "types": "index.d.ts", "typesPublisherContentHash": "f057cc6059959f07a6b87daf686e1054cce9899f3194a02a4cc50a1aa5904baf", "version": "1.5.5"}