# QR Code Generator for Files

A VS Code extension that generates QR codes for folder contents with intelligent processing and compression.

## Features

- **Multi-file Support**: Processes TypeScript (.ts), SCSS (.scss), and HTML (.html) files
- **Smart Processing**: 
  - Removes comments from TypeScript files
  - Compresses code by removing unnecessary whitespace
- **Intelligent Chunking**: Splits content into optimal chunks for QR code generation
- **Reverse Order Display**: Shows QR codes in reverse order as requested
- **Visual Interface**: Clean webview panel with navigation and content preview

## Usage

1. Right-click on any folder in the VS Code Explorer
2. Select "Generate QR Codes for Folder" from the context menu
3. The extension will:
   - Scan the folder for supported file types
   - Process and compress the files
   - Generate QR codes for the content chunks
   - Display them in a webview panel

## How It Works

1. **File Scanning**: Recursively scans the selected folder for .ts, .scss, and .html files
2. **Content Processing**: 
   - Removes single-line (//) and multi-line (/* */) comments from TypeScript files
   - Compresses all files by removing extra whitespace and formatting
3. **Chunking**: Splits the combined content into chunks that fit within QR code capacity limits
4. **QR Generation**: Creates QR codes for each chunk in reverse order
5. **Display**: Shows all QR codes in a webview with content previews and navigation

## Technical Details

- **Chunk Size**: Limited to ~2000 bytes per QR code for reliable scanning
- **Error Correction**: Uses medium error correction level for balance of capacity and reliability
- **Image Format**: Generates PNG images at 512x512 resolution
- **File Exclusions**: Automatically skips common directories like node_modules, .git, dist, etc.

## Installation

1. Install dependencies: `npm install`
2. Compile TypeScript: `npm run compile`
3. Press F5 to run the extension in a new Extension Development Host window

## Requirements

- VS Code 1.74.0 or higher
- Node.js for development

## Extension Settings

This extension contributes the following commands:

- `qrCodeGenerator.generateForFolder`: Generate QR codes for a selected folder

## Known Issues

- Very large files may result in many QR code chunks
- Binary files are not supported (only text-based files)

## Release Notes

### 0.0.1

Initial release with core functionality:
- Folder scanning for TypeScript, SCSS, and HTML files
- Comment removal and code compression
- QR code generation with chunking
- Reverse order display in webview panel
