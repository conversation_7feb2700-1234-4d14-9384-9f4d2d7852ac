{"_from": "pngjs@^5.0.0", "_id": "pngjs@5.0.0", "_inBundle": false, "_integrity": "sha512-40QW5YalBNfQo5yRYmiw7Yz6TKKVr3h6970B2YE+3fQpsWcrbj1PzJgxeJ19DRQjhMbKPIuMY8rFaXc8moolVw==", "_location": "/pngjs", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "pngjs@^5.0.0", "name": "pngjs", "escapedName": "pngjs", "rawSpec": "^5.0.0", "saveSpec": null, "fetchSpec": "^5.0.0"}, "_requiredBy": ["/qrcode"], "_resolved": "https://registry.npmjs.org/pngjs/-/pngjs-5.0.0.tgz", "_shasum": "e79dd2b215767fd9c04561c01236df960bce7fbb", "_spec": "pngjs@^5.0.0", "_where": "C:\\repos\\test\\node_modules\\qrcode", "bugs": {"url": "https://github.com/lukeapage/pngjs/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>"}, {"name": "Gaurav Mali"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "lian<PERSON><PERSON>g"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "toriningen"}, {"name": "<PERSON>"}], "deprecated": false, "description": "PNG encoder/decoder in pure JS, supporting any bit size & interlace, async & sync with full test suite.", "devDependencies": {"browserify": "16.5.1", "buffer-equal": "1.0.0", "codecov": "3.6.5", "connect": "3.7.0", "eslint": "6.8.0", "eslint-config-prettier": "6.10.1", "nyc": "15.0.1", "prettier": "2.0.4", "puppeteer": "2.1.1", "serve-static": "1.14.1", "tap-dot": "2.0.0", "tape": "4.13.2"}, "directories": {"lib": "lib", "example": "examples", "test": "test"}, "engines": {"node": ">=10.13.0"}, "homepage": "https://github.com/lukeapage/pngjs", "keywords": ["PNG", "decoder", "encoder", "js-png", "node-png", "parser", "png", "png-js", "png-parse", "pngjs"], "license": "MIT", "main": "./lib/png.js", "name": "pngjs", "repository": {"type": "git", "url": "git://github.com/lukeapage/pngjs.git"}, "scripts": {"browserify": "browserify lib/png.js --standalone png > browser.js", "build": "yarn prepublish", "coverage": "nyc --reporter=lcov --reporter=text-summary tape test/*-spec.js nolarge", "lint": "eslint .", "prepublish": "yarn browserify", "prettier:check": "prettier --check .", "prettier:write": "prettier --write .", "test": "yarn lint && yarn prettier:check && tape test/*-spec.js | tap-dot && node test/run-compare"}, "version": "5.0.0"}