{"_from": "find-up@^4.1.0", "_id": "find-up@4.1.0", "_inBundle": false, "_integrity": "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==", "_location": "/find-up", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "find-up@^4.1.0", "name": "find-up", "escapedName": "find-up", "rawSpec": "^4.1.0", "saveSpec": null, "fetchSpec": "^4.1.0"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "_shasum": "97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19", "_spec": "find-up@^4.1.0", "_where": "C:\\repos\\test\\node_modules\\yargs", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/find-up/issues"}, "bundleDependencies": false, "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "deprecated": false, "description": "Find a file or directory by walking up parent directories", "devDependencies": {"ava": "^2.1.0", "is-path-inside": "^2.1.0", "tempy": "^0.3.0", "tsd": "^0.7.3", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/find-up#readme", "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "walk", "walking", "path"], "license": "MIT", "name": "find-up", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/find-up.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "4.1.0"}