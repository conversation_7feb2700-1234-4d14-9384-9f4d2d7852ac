{"_from": "wrap-ansi@^6.2.0", "_id": "wrap-ansi@6.2.0", "_inBundle": false, "_integrity": "sha512-r6lPcBGxZXlIcymEu7InxDMhdW0KDxpLgoFLcguasxCaJ/SOIZwINatK9KY/tf+ZrlywOKU0UDj3ATXUBfxJXA==", "_location": "/wrap-ansi", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "wrap-ansi@^6.2.0", "name": "wrap-ansi", "escapedName": "wrap-ansi", "rawSpec": "^6.2.0", "saveSpec": null, "fetchSpec": "^6.2.0"}, "_requiredBy": ["/cliui"], "_resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-6.2.0.tgz", "_shasum": "e9393ba07102e6c91a3b221478f0257cd2856e53", "_spec": "wrap-ansi@^6.2.0", "_where": "C:\\repos\\test\\node_modules\\cliui", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/wrap-ansi/issues"}, "bundleDependencies": false, "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "deprecated": false, "description": "Wordwrap a string with ANSI escape codes", "devDependencies": {"ava": "^2.1.0", "chalk": "^2.4.2", "coveralls": "^3.0.3", "has-ansi": "^3.0.0", "nyc": "^14.1.1", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js"], "homepage": "https://github.com/chalk/wrap-ansi#readme", "keywords": ["wrap", "break", "wordwrap", "wordbreak", "linewrap", "ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "name": "wrap-ansi", "repository": {"type": "git", "url": "git+https://github.com/chalk/wrap-ansi.git"}, "scripts": {"test": "xo && nyc ava"}, "version": "6.2.0"}