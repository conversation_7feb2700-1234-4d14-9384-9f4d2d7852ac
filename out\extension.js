"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
const fileProcessor_1 = require("./fileProcessor");
const qrGenerator_1 = require("./qrGenerator");
const webview_1 = require("./webview");
function activate(context) {
    console.log('QR Code Generator extension is now active!');
    const disposable = vscode.commands.registerCommand('qrCodeGenerator.generateForFolder', async (uri) => {
        try {
            // Get the folder path
            const folderPath = uri.fsPath;
            // Show progress indicator
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: "Generating QR Codes",
                cancellable: false
            }, async (progress) => {
                progress.report({ increment: 0, message: "Scanning files..." });
                // Process files
                const fileProcessor = new fileProcessor_1.FileProcessor();
                const files = await fileProcessor.scanFolder(folderPath);
                progress.report({ increment: 30, message: "Processing and compressing files..." });
                const processedContent = await fileProcessor.processFiles(files);
                progress.report({ increment: 60, message: "Generating QR codes..." });
                // Generate QR codes
                const qrGenerator = new qrGenerator_1.QRGenerator();
                const qrCodes = await qrGenerator.generateQRCodes(processedContent);
                progress.report({ increment: 90, message: "Creating display..." });
                // Show QR codes in webview
                const webviewProvider = new webview_1.QRWebviewProvider(context.extensionUri);
                await webviewProvider.showQRCodes(qrCodes);
                progress.report({ increment: 100, message: "Complete!" });
            });
        }
        catch (error) {
            vscode.window.showErrorMessage(`Error generating QR codes: ${error}`);
        }
    });
    context.subscriptions.push(disposable);
}
exports.activate = activate;
function deactivate() { }
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map