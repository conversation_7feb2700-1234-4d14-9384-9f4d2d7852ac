{"name": "qr-code-generator", "displayName": "QR Code Generator for Files", "description": "Generate QR codes for folder contents with compression and chunking", "version": "0.0.1", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"commands": [{"command": "qrCodeGenerator.generateForFolder", "title": "Generate QR Codes for Folder", "category": "QR Code"}], "menus": {"explorer/context": [{"command": "qrCodeGenerator.generateForFolder", "when": "explorerResourceIsFolder", "group": "navigation"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {"qrcode": "^1.5.3", "@types/qrcode": "^1.5.2"}}