{"_from": "set-blocking@^2.0.0", "_id": "set-blocking@2.0.0", "_inBundle": false, "_integrity": "sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==", "_location": "/set-blocking", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "set-blocking@^2.0.0", "name": "set-blocking", "escapedName": "set-blocking", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/set-blocking/-/set-blocking-2.0.0.tgz", "_shasum": "045f9782d011ae9a6803ddd382b24392b3d890f7", "_spec": "set-blocking@^2.0.0", "_where": "C:\\repos\\test\\node_modules\\yargs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/set-blocking/issues"}, "bundleDependencies": false, "deprecated": false, "description": "set blocking stdio and stderr ensuring that terminal output does not truncate", "devDependencies": {"chai": "^3.5.0", "coveralls": "^2.11.9", "mocha": "^2.4.5", "nyc": "^6.4.4", "standard": "^7.0.1", "standard-version": "^2.2.1"}, "files": ["index.js", "LICENSE.txt"], "homepage": "https://github.com/yargs/set-blocking#readme", "keywords": ["flush", "terminal", "blocking", "shim", "stdio", "stderr"], "license": "ISC", "main": "index.js", "name": "set-blocking", "repository": {"type": "git", "url": "git+https://github.com/yargs/set-blocking.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "pretest": "standard", "test": "nyc mocha ./test/*.js", "version": "standard-version"}, "version": "2.0.0"}