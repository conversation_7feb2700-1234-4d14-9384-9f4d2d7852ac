{"_from": "@types/node@*", "_id": "@types/node@22.15.30", "_inBundle": false, "_integrity": "sha512-6Q7lr06bEHdlfplU6YRbgG1SFBdlsfNC4/lX+SkhiTs0cpJkOElmWls8PxDFv4yY/xKb8Y6SO0OmSX4wgqTZbA==", "_location": "/@types/qrcode/@types/node", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/node@*", "name": "@types/node", "escapedName": "@types%2fnode", "scope": "@types", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/@types/qrcode"], "_resolved": "https://registry.npmjs.org/@types/node/-/node-22.15.30.tgz", "_shasum": "3a20431783e28dd0b0326f84ab386a2ec81d921d", "_spec": "@types/node@*", "_where": "C:\\repos\\test\\node_modules\\@types\\qrcode", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "Microsoft TypeScript", "url": "https://github.com/Microsoft"}, {"name": "<PERSON>", "url": "https://github.com/jkomyno"}, {"name": "Alvis <PERSON>", "url": "https://github.com/alvis"}, {"name": "<PERSON>", "url": "https://github.com/r3nya"}, {"name": "<PERSON>", "url": "https://github.com/btoueg"}, {"name": "Chigozirim C.", "url": "https://github.com/smac89"}, {"name": "<PERSON>", "url": "https://github.com/touffy"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/DeividasBakanas"}, {"name": "<PERSON>", "url": "https://github.com/eyqs"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>-<PERSON>-CK"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/hoo29"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kjin"}, {"name": "<PERSON>", "url": "https://github.com/ajafff"}, {"name": "Lishude", "url": "https://github.com/islishude"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mwiktorczyk"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/mohsen1"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/galkin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/parambirs"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon"}, {"name": "<PERSON>", "url": "https://github.com/ThomasdenH"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/WilcoBakker"}, {"name": "wwwy3y3", "url": "https://github.com/wwwy3y3"}, {"name": "<PERSON>", "url": "https://github.com/samuela"}, {"name": "<PERSON>", "url": "https://github.com/kuehlein"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/bhongy"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/chyzwar"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/trivikr"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/yoursunny"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/qwelias"}, {"name": "ExE Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/addaleax"}, {"name": "<PERSON>", "url": "https://github.com/victorperin"}, {"name": "NodeJS Contributors", "url": "https://github.com/NodeJS"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/LinusU"}, {"name": "wafuwafu13", "url": "https://github.com/wafuwafu13"}, {"name": "<PERSON>", "url": "https://github.com/mcollina"}, {"name": "<PERSON>", "url": "https://github.com/Semigradsky"}, {"name": "<PERSON>", "url": "https://github.com/Renegade334"}], "dependencies": {"undici-types": "~6.21.0"}, "deprecated": false, "description": "TypeScript definitions for node", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/node", "license": "MIT", "main": "", "name": "@types/node", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/node"}, "scripts": {}, "typeScriptVersion": "5.1", "types": "index.d.ts", "typesPublisherContentHash": "e608b189ccd6623034719d736dfcdaf6bae11f1e8fe989b19c4a6d3221277a64", "typesVersions": {"<=5.6": {"*": ["ts5.6/*"]}}, "version": "22.15.30"}