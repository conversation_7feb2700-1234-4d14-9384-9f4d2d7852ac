{"version": 3, "file": "qrGenerator.js", "sourceRoot": "", "sources": ["../src/qrGenerator.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAWjC,MAAa,WAAW;IAAxB;QACI,oFAAoF;QACnE,iBAAY,GAAG,IAAI,CAAC,CAAC,2CAA2C;IAiHrF,CAAC;IA/GG,KAAK,CAAC,eAAe,CAAC,cAA+B;QACjD,4CAA4C;QAC5C,MAAM,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;QAE1D,oBAAoB;QACpB,MAAM,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAC;QAElD,2CAA2C;QAC3C,MAAM,cAAc,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;QAExC,mCAAmC;QACnC,MAAM,OAAO,GAAiB,EAAE,CAAC;QACjC,MAAM,WAAW,GAAG,cAAc,CAAC,MAAM,CAAC;QAE1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,MAAM,KAAK,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;YAEhC,IAAI;gBACA,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE;oBAC1C,oBAAoB,EAAE,GAAG;oBACzB,IAAI,EAAE,WAAW;oBACjB,MAAM,EAAE,CAAC;oBACT,KAAK,EAAE;wBACH,IAAI,EAAE,SAAS;wBACf,KAAK,EAAE,SAAS;qBACnB;oBACD,KAAK,EAAE,GAAG;iBACb,CAAC,CAAC;gBAEH,OAAO,CAAC,IAAI,CAAC;oBACT,OAAO;oBACP,UAAU,EAAE,CAAC,GAAG,CAAC;oBACjB,WAAW;oBACX,OAAO,EAAE,KAAK;oBACd,IAAI,EAAE,KAAK,CAAC,MAAM;iBACrB,CAAC,CAAC;aAEN;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,sCAAsC,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;aACxE;SACJ;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAEO,YAAY,CAAC,cAA+B;QAChD,IAAI,QAAQ,GAAG,EAAE,CAAC;QAElB,KAAK,MAAM,IAAI,IAAI,cAAc,EAAE;YAC/B,QAAQ,IAAI,eAAe,IAAI,CAAC,YAAY,QAAQ,CAAC;YACrD,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC;YACzB,QAAQ,IAAI,IAAI,CAAC;SACpB;QAED,OAAO,QAAQ,CAAC;IACpB,CAAC;IAEO,YAAY,CAAC,OAAe;QAChC,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,YAAY,GAAG,EAAE,CAAC;QAEtB,gFAAgF;QAChF,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAElC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;YACtB,wDAAwD;YACxD,IAAI,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC,YAAY,EAAE;gBAC3D,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;oBACzB,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;oBAC1B,YAAY,GAAG,EAAE,CAAC;iBACrB;gBAED,yCAAyC;gBACzC,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE;oBACjC,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;oBAC5C,MAAM,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxC,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;iBACpD;qBAAM;oBACH,YAAY,GAAG,IAAI,CAAC;iBACvB;aACJ;iBAAM;gBACH,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;oBACzB,YAAY,IAAI,IAAI,CAAC;iBACxB;gBACD,YAAY,IAAI,IAAI,CAAC;aACxB;SACJ;QAED,uCAAuC;QACvC,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;YACzB,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SAC7B;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,aAAa,CAAC,IAAY;QAC9B,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,SAAS,GAAG,IAAI,CAAC;QAErB,OAAO,SAAS,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE;YACzC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC;YACvD,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;SACtD;QAED,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;YACtB,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC1B;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;CACJ;AAnHD,kCAmHC"}