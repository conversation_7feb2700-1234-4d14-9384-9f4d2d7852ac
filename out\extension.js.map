{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,mDAAgD;AAChD,+CAA4C;AAC5C,uCAA8C;AAE9C,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;IAE1D,MAAM,UAAU,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mCAAmC,EAAE,KAAK,EAAE,GAAe,EAAE,EAAE;QAC9G,IAAI;YACA,sBAAsB;YACtB,MAAM,UAAU,GAAG,GAAG,CAAC,MAAM,CAAC;YAE9B,0BAA0B;YAC1B,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;gBAC7B,QAAQ,EAAE,MAAM,CAAC,gBAAgB,CAAC,YAAY;gBAC9C,KAAK,EAAE,qBAAqB;gBAC5B,WAAW,EAAE,KAAK;aACrB,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE;gBAClB,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE,mBAAmB,EAAE,CAAC,CAAC;gBAEhE,gBAAgB;gBAChB,MAAM,aAAa,GAAG,IAAI,6BAAa,EAAE,CAAC;gBAC1C,MAAM,KAAK,GAAG,MAAM,aAAa,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;gBAEzD,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,qCAAqC,EAAE,CAAC,CAAC;gBAEnF,MAAM,gBAAgB,GAAG,MAAM,aAAa,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBAEjE,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,wBAAwB,EAAE,CAAC,CAAC;gBAEtE,oBAAoB;gBACpB,MAAM,WAAW,GAAG,IAAI,yBAAW,EAAE,CAAC;gBACtC,MAAM,OAAO,GAAG,MAAM,WAAW,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC;gBAEpE,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,EAAE,EAAE,OAAO,EAAE,qBAAqB,EAAE,CAAC,CAAC;gBAEnE,2BAA2B;gBAC3B,MAAM,eAAe,GAAG,IAAI,2BAAiB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;gBACpE,MAAM,eAAe,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBAE3C,QAAQ,CAAC,MAAM,CAAC,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;YAC9D,CAAC,CAAC,CAAC;SAEN;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;SACzE;IACL,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC3C,CAAC;AA7CD,4BA6CC;AAED,SAAgB,UAAU,KAAI,CAAC;AAA/B,gCAA+B"}