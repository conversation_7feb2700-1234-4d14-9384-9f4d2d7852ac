{"_from": "require-main-filename@^2.0.0", "_id": "require-main-filename@2.0.0", "_inBundle": false, "_integrity": "sha512-NKN5kMDylKuldxYLSUfrbo5Tuzh4hd+2E8NPPX02mZtn1VuREQToYe/ZdlJy+J3uCpfaiGF05e7B8W0iXbQHmg==", "_location": "/require-main-filename", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "require-main-filename@^2.0.0", "name": "require-main-filename", "escapedName": "require-main-filename", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/require-main-filename/-/require-main-filename-2.0.0.tgz", "_shasum": "d0b329ecc7cc0f61649f62215be69af54aa8989b", "_spec": "require-main-filename@^2.0.0", "_where": "C:\\repos\\test\\node_modules\\yargs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/require-main-filename/issues"}, "bundleDependencies": false, "deprecated": false, "description": "shim for require.main.filename() that works in as many environments as possible", "devDependencies": {"chai": "^4.0.0", "standard": "^10.0.3", "standard-version": "^4.0.0", "tap": "^11.0.0"}, "files": ["index.js"], "homepage": "https://github.com/yargs/require-main-filename#readme", "keywords": ["require", "shim", "iisnode"], "license": "ISC", "main": "index.js", "name": "require-main-filename", "repository": {"type": "git", "url": "git+ssh://**************/yargs/require-main-filename.git"}, "scripts": {"pretest": "standard", "release": "standard-version", "test": "tap --coverage test.js"}, "version": "2.0.0"}