{"_from": "ansi-styles@^4.0.0", "_id": "ansi-styles@4.3.0", "_inBundle": false, "_integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "_location": "/ansi-styles", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ansi-styles@^4.0.0", "name": "ansi-styles", "escapedName": "ansi-styles", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/wrap-ansi"], "_resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "_shasum": "edd803628ae71c04c85ae7a0906edad34b648937", "_spec": "ansi-styles@^4.0.0", "_where": "C:\\repos\\test\\node_modules\\wrap-ansi", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/chalk/ansi-styles/issues"}, "bundleDependencies": false, "dependencies": {"color-convert": "^2.0.1"}, "deprecated": false, "description": "ANSI escape codes for styling strings in the terminal", "devDependencies": {"@types/color-convert": "^1.9.0", "ava": "^2.3.0", "svg-term-cli": "^2.1.1", "tsd": "^0.11.0", "xo": "^0.25.3"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "funding": "https://github.com/chalk/ansi-styles?sponsor=1", "homepage": "https://github.com/chalk/ansi-styles#readme", "keywords": ["ansi", "styles", "color", "colour", "colors", "terminal", "console", "cli", "string", "tty", "escape", "formatting", "rgb", "256", "shell", "xterm", "log", "logging", "command-line", "text"], "license": "MIT", "name": "ansi-styles", "repository": {"type": "git", "url": "git+https://github.com/chalk/ansi-styles.git"}, "scripts": {"screenshot": "svg-term --command='node screenshot' --out=screenshot.svg --padding=3 --width=55 --height=3 --at=1000 --no-cursor", "test": "xo && ava && tsd"}, "version": "4.3.0"}