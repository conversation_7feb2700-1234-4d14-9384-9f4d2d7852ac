import * as vscode from 'vscode';
import { FileProcessor } from './fileProcessor';
import { QRGenerator } from './qrGenerator';
import { QRWebviewProvider } from './webview';

export function activate(context: vscode.ExtensionContext) {
    console.log('QR Code Generator extension is now active!');

    const disposable = vscode.commands.registerCommand('qrCodeGenerator.generateForFolder', async (uri: vscode.Uri) => {
        try {
            // Get the folder path
            const folderPath = uri.fsPath;
            
            // Show progress indicator
            await vscode.window.withProgress({
                location: vscode.ProgressLocation.Notification,
                title: "Generating QR Codes",
                cancellable: false
            }, async (progress) => {
                progress.report({ increment: 0, message: "Scanning files..." });
                
                // Process files
                const fileProcessor = new FileProcessor();
                const files = await fileProcessor.scanFolder(folderPath);
                
                progress.report({ increment: 30, message: "Processing and compressing files..." });
                
                const processedContent = await fileProcessor.processFiles(files);
                
                progress.report({ increment: 60, message: "Generating QR codes..." });
                
                // Generate QR codes
                const qrGenerator = new QRGenerator();
                const qrCodes = await qrGenerator.generateQRCodes(processedContent);
                
                progress.report({ increment: 90, message: "Creating display..." });
                
                // Show QR codes in webview
                const webviewProvider = new QRWebviewProvider(context.extensionUri);
                await webviewProvider.showQRCodes(qrCodes);
                
                progress.report({ increment: 100, message: "Complete!" });
            });
            
        } catch (error) {
            vscode.window.showErrorMessage(`Error generating QR codes: ${error}`);
        }
    });

    context.subscriptions.push(disposable);
}

export function deactivate() {}
