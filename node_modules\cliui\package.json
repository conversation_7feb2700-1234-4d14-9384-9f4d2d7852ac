{"_from": "cliui@^6.0.0", "_id": "cliui@6.0.0", "_inBundle": false, "_integrity": "sha512-t6wbgtoCXvAzst7QgXxJYqPt0usEfbgQdftEPbLL/cvv6HPE5VgvqCuAIDR0NgU52ds6rFwqrgakNLrHEjCbrQ==", "_location": "/cliui", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cliui@^6.0.0", "name": "cliui", "escapedName": "cliui", "rawSpec": "^6.0.0", "saveSpec": null, "fetchSpec": "^6.0.0"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/cliui/-/cliui-6.0.0.tgz", "_shasum": "511d702c0c4e41ca156d7d0e96021f23e13225b1", "_spec": "cliui@^6.0.0", "_where": "C:\\repos\\test\\node_modules\\yargs", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/yargs/cliui/issues"}, "bundleDependencies": false, "config": {"blanket": {"pattern": ["index.js"], "data-cover-never": ["node_modules", "test"], "output-reporter": "spec"}}, "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^6.2.0"}, "deprecated": false, "description": "easily create complex multi-column command-line-interfaces", "devDependencies": {"chai": "^4.2.0", "chalk": "^3.0.0", "coveralls": "^3.0.3", "mocha": "^6.2.2", "nyc": "^14.1.1", "standard": "^12.0.1"}, "engine": {"node": ">=8"}, "files": ["index.js"], "homepage": "https://github.com/yargs/cliui#readme", "keywords": ["cli", "command-line", "layout", "design", "console", "wrap", "table"], "license": "ISC", "main": "index.js", "name": "cliui", "repository": {"type": "git", "url": "git+ssh://**************/yargs/cliui.git"}, "scripts": {"coverage": "nyc --reporter=text-lcov mocha | coveralls", "pretest": "standard", "test": "nyc mocha"}, "standard": {"ignore": ["**/example/**"], "globals": ["it"]}, "version": "6.0.0"}