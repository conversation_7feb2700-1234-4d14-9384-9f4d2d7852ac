import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

export interface ProcessedFile {
    relativePath: string;
    content: string;
    originalSize: number;
    compressedSize: number;
}

export class FileProcessor {
    private readonly supportedExtensions = ['.ts', '.scss', '.html'];

    async scanFolder(folderPath: string): Promise<string[]> {
        const files: string[] = [];
        
        const scanDirectory = async (dirPath: string): Promise<void> => {
            const entries = await fs.promises.readdir(dirPath, { withFileTypes: true });
            
            for (const entry of entries) {
                const fullPath = path.join(dirPath, entry.name);
                
                if (entry.isDirectory()) {
                    // Skip node_modules and other common directories
                    if (!['node_modules', '.git', 'dist', 'out', 'build'].includes(entry.name)) {
                        await scanDirectory(fullPath);
                    }
                } else if (entry.isFile()) {
                    const ext = path.extname(entry.name).toLowerCase();
                    if (this.supportedExtensions.includes(ext)) {
                        files.push(fullPath);
                    }
                }
            }
        };

        await scanDirectory(folderPath);
        return files;
    }

    async processFiles(filePaths: string[]): Promise<ProcessedFile[]> {
        const processedFiles: ProcessedFile[] = [];
        
        for (const filePath of filePaths) {
            try {
                const content = await fs.promises.readFile(filePath, 'utf-8');
                const ext = path.extname(filePath).toLowerCase();
                
                let processedContent = content;
                
                // Remove comments for TypeScript files
                if (ext === '.ts') {
                    processedContent = this.removeTypeScriptComments(processedContent);
                }
                
                // Compress all files
                processedContent = this.compressCode(processedContent);
                
                // Get relative path for display
                const relativePath = path.relative(process.cwd(), filePath);
                
                processedFiles.push({
                    relativePath,
                    content: processedContent,
                    originalSize: content.length,
                    compressedSize: processedContent.length
                });
                
            } catch (error) {
                console.error(`Error processing file ${filePath}:`, error);
            }
        }
        
        return processedFiles;
    }

    private removeTypeScriptComments(content: string): string {
        // Remove single-line comments
        content = content.replace(/\/\/.*$/gm, '');
        
        // Remove multi-line comments
        content = content.replace(/\/\*[\s\S]*?\*\//g, '');
        
        return content;
    }

    private compressCode(content: string): string {
        // Remove extra whitespace and newlines
        return content
            .replace(/\s+/g, ' ')           // Replace multiple spaces with single space
            .replace(/\n\s*/g, '\n')        // Remove leading spaces on new lines
            .replace(/\n+/g, '\n')          // Replace multiple newlines with single newline
            .replace(/;\s+/g, ';')          // Remove spaces after semicolons
            .replace(/,\s+/g, ',')          // Remove spaces after commas
            .replace(/{\s+/g, '{')          // Remove spaces after opening braces
            .replace(/\s+}/g, '}')          // Remove spaces before closing braces
            .replace(/\(\s+/g, '(')         // Remove spaces after opening parentheses
            .replace(/\s+\)/g, ')')         // Remove spaces before closing parentheses
            .trim();
    }
}
