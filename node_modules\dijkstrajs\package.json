{"_from": "dijkstrajs@^1.0.1", "_id": "dijkstrajs@1.0.3", "_inBundle": false, "_integrity": "sha512-qiSlmBq9+BCdCA/L46dw8Uy93mloxsPSbwnm5yrKn2vMPiy8KyAskTF6zuV/j5BMsmOGZDPs7KjU+mjb670kfA==", "_location": "/dijkstrajs", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "dijkstrajs@^1.0.1", "name": "dijkstrajs", "escapedName": "dijkstrajs", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/qrcode"], "_resolved": "https://registry.npmjs.org/dijkstrajs/-/dijkstrajs-1.0.3.tgz", "_shasum": "4c8dbdea1f0f6478bff94d9c49c784d623e4fc23", "_spec": "dijkstrajs@^1.0.1", "_where": "C:\\repos\\test\\node_modules\\qrcode", "bugs": {"url": "https://github.com/tcort/dijkstrajs/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A simple JavaScript implementation of Dijkstra's single-source shortest-paths algorithm.", "devDependencies": {"expect.js": "^0.3.1", "jshint": "^2.13.6", "mocha": "^10.2.0"}, "homepage": "https://github.com/tcort/dijkstrajs", "jshintConfig": {"bitwise": true, "curly": true, "eqeqeq": true, "forin": true, "freeze": true, "globalstrict": true, "immed": true, "indent": 4, "moz": true, "newcap": true, "noarg": true, "node": true, "noempty": true, "nonew": true, "trailing": true, "undef": true, "smarttabs": true, "strict": true, "validthis": true, "globals": {"describe": false, "it": false, "before": false, "beforeEach": false, "after": false, "afterEach": false}}, "keywords": ["<PERSON><PERSON><PERSON>", "shortest", "path", "search", "graph"], "license": "MIT", "main": "dijkstra.js", "name": "dijkstrajs", "repository": {"type": "git", "url": "git://github.com/tcort/dijkstrajs.git"}, "scripts": {"pretest": "jshint dijkstra.js", "test": "mocha -R spec"}, "version": "1.0.3"}