{"_from": "p-locate@^4.1.0", "_id": "p-locate@4.1.0", "_inBundle": false, "_integrity": "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==", "_location": "/p-locate", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "p-locate@^4.1.0", "name": "p-locate", "escapedName": "p-locate", "rawSpec": "^4.1.0", "saveSpec": null, "fetchSpec": "^4.1.0"}, "_requiredBy": ["/locate-path"], "_resolved": "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz", "_shasum": "a3428bb7088b3a60292f66919278b7c297ad4f07", "_spec": "p-locate@^4.1.0", "_where": "C:\\repos\\test\\node_modules\\locate-path", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/p-locate/issues"}, "bundleDependencies": false, "dependencies": {"p-limit": "^2.2.0"}, "deprecated": false, "description": "Get the first fulfilled promise that satisfies the provided testing function", "devDependencies": {"ava": "^1.4.1", "delay": "^4.1.0", "in-range": "^1.0.0", "time-span": "^3.0.0", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/p-locate#readme", "keywords": ["promise", "locate", "find", "finder", "search", "searcher", "test", "array", "collection", "iterable", "iterator", "race", "fulfilled", "fastest", "async", "await", "promises", "bluebird"], "license": "MIT", "name": "p-locate", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/p-locate.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "4.1.0"}