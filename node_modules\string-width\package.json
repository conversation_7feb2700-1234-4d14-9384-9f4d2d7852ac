{"_from": "string-width@^4.2.0", "_id": "string-width@4.2.3", "_inBundle": false, "_integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "_location": "/string-width", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "string-width@^4.2.0", "name": "string-width", "escapedName": "string-width", "rawSpec": "^4.2.0", "saveSpec": null, "fetchSpec": "^4.2.0"}, "_requiredBy": ["/cliui", "/wrap-ansi", "/yargs"], "_resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "_shasum": "269c7117d27b05ad2e536830a8ec895ef9c6d010", "_spec": "string-width@^4.2.0", "_where": "C:\\repos\\test\\node_modules\\yargs", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/string-width/issues"}, "bundleDependencies": false, "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "deprecated": false, "description": "Get the visual width of a string - the number of columns required to display it", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.1", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/sindresorhus/string-width#readme", "keywords": ["string", "character", "unicode", "width", "visual", "column", "columns", "fullwidth", "full-width", "full", "ansi", "escape", "codes", "cli", "command-line", "terminal", "console", "cjk", "chinese", "japanese", "korean", "fixed-width"], "license": "MIT", "name": "string-width", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/string-width.git"}, "scripts": {"test": "xo && ava && tsd"}, "version": "4.2.3"}