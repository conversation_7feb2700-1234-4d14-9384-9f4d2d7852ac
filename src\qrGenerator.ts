import * as QRCode from 'qrcode';
import { ProcessedFile } from './fileProcessor';

export interface QRCodeData {
    dataUrl: string;
    chunkIndex: number;
    totalChunks: number;
    content: string;
    size: number;
}

export class QRGenerator {
    // QR Code capacity limits (approximate bytes for different error correction levels)
    private readonly maxChunkSize = 2000; // Conservative limit for reliable scanning

    async generateQRCodes(processedFiles: ProcessedFile[]): Promise<QRCodeData[]> {
        // Combine all file contents with separators
        const combinedContent = this.combineFiles(processedFiles);
        
        // Split into chunks
        const chunks = this.chunkContent(combinedContent);
        
        // Reverse the order of chunks as requested
        const reversedChunks = chunks.reverse();
        
        // Generate QR codes for each chunk
        const qrCodes: QRCodeData[] = [];
        const totalChunks = reversedChunks.length;
        
        for (let i = 0; i < reversedChunks.length; i++) {
            const chunk = reversedChunks[i];
            
            try {
                const dataUrl = await QRCode.toDataURL(chunk, {
                    errorCorrectionLevel: 'M',
                    type: 'image/png',
                    margin: 1,
                    color: {
                        dark: '#000000',
                        light: '#FFFFFF'
                    },
                    width: 512
                });

                qrCodes.push({
                    dataUrl,
                    chunkIndex: i + 1,
                    totalChunks,
                    content: chunk,
                    size: chunk.length
                });
                
            } catch (error) {
                console.error(`Error generating QR code for chunk ${i + 1}:`, error);
            }
        }
        
        return qrCodes;
    }

    private combineFiles(processedFiles: ProcessedFile[]): string {
        let combined = '';
        
        for (const file of processedFiles) {
            combined += `\n--- FILE: ${file.relativePath} ---\n`;
            combined += file.content;
            combined += '\n';
        }
        
        return combined;
    }

    private chunkContent(content: string): string[] {
        const chunks: string[] = [];
        let currentChunk = '';
        
        // Split content into lines to avoid breaking in the middle of important content
        const lines = content.split('\n');
        
        for (const line of lines) {
            // Check if adding this line would exceed the chunk size
            if (currentChunk.length + line.length + 1 > this.maxChunkSize) {
                if (currentChunk.length > 0) {
                    chunks.push(currentChunk);
                    currentChunk = '';
                }
                
                // If a single line is too long, split it
                if (line.length > this.maxChunkSize) {
                    const lineChunks = this.splitLongLine(line);
                    chunks.push(...lineChunks.slice(0, -1));
                    currentChunk = lineChunks[lineChunks.length - 1];
                } else {
                    currentChunk = line;
                }
            } else {
                if (currentChunk.length > 0) {
                    currentChunk += '\n';
                }
                currentChunk += line;
            }
        }
        
        // Add the last chunk if it has content
        if (currentChunk.length > 0) {
            chunks.push(currentChunk);
        }
        
        return chunks;
    }

    private splitLongLine(line: string): string[] {
        const chunks: string[] = [];
        let remaining = line;
        
        while (remaining.length > this.maxChunkSize) {
            chunks.push(remaining.substring(0, this.maxChunkSize));
            remaining = remaining.substring(this.maxChunkSize);
        }
        
        if (remaining.length > 0) {
            chunks.push(remaining);
        }
        
        return chunks;
    }
}
