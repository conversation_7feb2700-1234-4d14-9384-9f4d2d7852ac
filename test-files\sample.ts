// This is a sample TypeScript file
// It contains comments that should be removed

interface User {
    id: number;
    name: string;
    email: string;
}

/* 
 * Multi-line comment
 * This should also be removed
 */
class UserService {
    private users: User[] = [];

    // Add a new user
    addUser(user: User): void {
        this.users.push(user);
    }

    // Get user by ID
    getUserById(id: number): User | undefined {
        return this.users.find(user => user.id === id);
    }

    /* Get all users */
    getAllUsers(): User[] {
        return this.users;
    }
}
