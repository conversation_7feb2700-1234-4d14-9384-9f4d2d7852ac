{"_from": "which-module@^2.0.0", "_id": "which-module@2.0.1", "_inBundle": false, "_integrity": "sha512-iBdZ57RDvnOR9AGBhML2vFZf7h8vmBjhoaZqODJBFWHVtKkDmKuHai3cx5PgVMrX5YDNp27AofYbAwctSS+vhQ==", "_location": "/which-module", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "which-module@^2.0.0", "name": "which-module", "escapedName": "which-module", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/which-module/-/which-module-2.0.1.tgz", "_shasum": "776b1fe35d90aebe99e8ac15eb24093389a4a409", "_spec": "which-module@^2.0.0", "_where": "C:\\repos\\test\\node_modules\\yargs", "author": {"name": "nexdrew"}, "bugs": {"url": "https://github.com/nexdrew/which-module/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Find the module object for something that was require()d", "devDependencies": {"ava": "^2.0.0", "coveralls": "^3.0.3", "nyc": "^14.0.0", "standard": "^14.0.0", "standard-version": "^7.0.0"}, "files": ["index.js"], "homepage": "https://github.com/nexdrew/which-module#readme", "keywords": ["which", "module", "exports", "filename", "require", "reverse", "lookup"], "license": "ISC", "main": "index.js", "name": "which-module", "repository": {"type": "git", "url": "git+https://github.com/nexdrew/which-module.git"}, "scripts": {"coverage": "nyc report --reporter=text-lcov | coveralls", "pretest": "standard", "release": "standard-version", "test": "nyc ava"}, "version": "2.0.1"}