{"_from": "require-directory@^2.1.1", "_id": "require-directory@2.1.1", "_inBundle": false, "_integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==", "_location": "/require-directory", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "require-directory@^2.1.1", "name": "require-directory", "escapedName": "require-directory", "rawSpec": "^2.1.1", "saveSpec": null, "fetchSpec": "^2.1.1"}, "_requiredBy": ["/yargs"], "_resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "_shasum": "8c64ad5fd30dab1c976e2344ffe7f792a6a6df42", "_spec": "require-directory@^2.1.1", "_where": "C:\\repos\\test\\node_modules\\yargs", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}, "bugs": {"url": "http://github.com/troygoode/node-require-directory/issues/"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://github.com/troygoode/"}], "deprecated": false, "description": "Recursively iterates over specified directory, require()'ing each file, and returning a nested hash structure containing those modules.", "devDependencies": {"jshint": "^2.6.0", "mocha": "^2.1.0"}, "engines": {"node": ">=0.10.0"}, "homepage": "https://github.com/troygoode/node-require-directory/", "keywords": ["require", "directory", "library", "recursive"], "license": "MIT", "main": "index.js", "name": "require-directory", "repository": {"type": "git", "url": "git://github.com/troygoode/node-require-directory.git"}, "scripts": {"lint": "jshint index.js test/test.js", "test": "mocha"}, "version": "2.1.1"}