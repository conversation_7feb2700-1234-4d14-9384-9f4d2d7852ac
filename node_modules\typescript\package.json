{"_from": "typescript@^4.9.4", "_id": "typescript@4.9.5", "_inBundle": false, "_integrity": "sha512-1FXk9E2Hm+QzZQ7z+McJiHL4NW1F2EzMu9Nq9i3zAaGqibafqYwCVU6WyWAuyQRRzOlxou8xZSyXLEN8oKj24g==", "_location": "/typescript", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "typescript@^4.9.4", "name": "typescript", "escapedName": "typescript", "rawSpec": "^4.9.4", "saveSpec": null, "fetchSpec": "^4.9.4"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/typescript/-/typescript-4.9.5.tgz", "_shasum": "095979f9bcc0d09da324d58d03ce8f8374cbe65a", "_spec": "typescript@^4.9.4", "_where": "C:\\repos\\test", "author": {"name": "Microsoft Corp."}, "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "browser": {"fs": false, "os": false, "path": false, "crypto": false, "buffer": false, "@microsoft/typescript-etw": false, "source-map-support": false, "inspector": false}, "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "bundleDependencies": false, "deprecated": false, "description": "TypeScript is a language for application scale JavaScript development", "devDependencies": {"@octokit/rest": "latest", "@types/chai": "latest", "@types/fancy-log": "^2.0.0", "@types/fs-extra": "^9.0.13", "@types/glob": "latest", "@types/gulp": "^4.0.9", "@types/gulp-concat": "latest", "@types/gulp-newer": "latest", "@types/gulp-rename": "latest", "@types/gulp-sourcemaps": "latest", "@types/merge2": "latest", "@types/microsoft__typescript-etw": "latest", "@types/minimist": "latest", "@types/mkdirp": "latest", "@types/mocha": "latest", "@types/ms": "latest", "@types/node": "latest", "@types/source-map-support": "latest", "@types/which": "^2.0.1", "@types/xml2js": "^0.4.11", "@typescript-eslint/eslint-plugin": "^5.33.1", "@typescript-eslint/parser": "^5.33.1", "@typescript-eslint/utils": "^5.33.1", "azure-devops-node-api": "^11.2.0", "chai": "latest", "chalk": "^4.1.2", "del": "^6.1.1", "diff": "^5.1.0", "eslint": "^8.22.0", "eslint-formatter-autolinkable-stylish": "^1.2.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsdoc": "^39.3.6", "eslint-plugin-local": "^1.0.0", "eslint-plugin-no-null": "^1.0.2", "fancy-log": "latest", "fs-extra": "^9.1.0", "glob": "latest", "gulp": "^4.0.2", "gulp-concat": "latest", "gulp-insert": "latest", "gulp-newer": "latest", "gulp-rename": "latest", "gulp-sourcemaps": "latest", "merge2": "latest", "minimist": "latest", "mkdirp": "latest", "mocha": "latest", "mocha-fivemat-progress-reporter": "latest", "ms": "^2.1.3", "node-fetch": "^3.2.10", "source-map-support": "latest", "typescript": "^4.8.4", "vinyl": "latest", "which": "^2.0.2", "xml2js": "^0.4.23"}, "engines": {"node": ">=4.2.0"}, "files": ["bin", "lib", "!lib/enu", "LICENSE.txt", "README.md", "SECURITY.md", "ThirdPartyNoticeText.txt", "!**/.gitattributes"], "homepage": "https://www.typescriptlang.org/", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript"], "license": "Apache-2.0", "main": "./lib/typescript.js", "name": "typescript", "overrides": {"es5-ext": "0.10.53"}, "packageManager": "npm@8.15.0", "repository": {"type": "git", "url": "git+https://github.com/Microsoft/TypeScript.git"}, "scripts": {"build": "npm run build:compiler && npm run build:tests", "build:compiler": "gulp local", "build:tests": "gulp tests", "clean": "gulp clean", "gulp": "gulp", "lint": "gulp lint", "setup-hooks": "node scripts/link-hooks.mjs", "start": "node lib/tsc", "test": "gulp runtests-parallel --light=false", "test:eslint-rules": "gulp run-eslint-rules-tests"}, "typings": "./lib/typescript.d.ts", "version": "4.9.5", "volta": {"node": "14.20.0", "npm": "8.15.0"}}