{"_from": "qrcode@^1.5.3", "_id": "qrcode@1.5.4", "_inBundle": false, "_integrity": "sha512-1ca71Zgiu6ORjHqFBDpnSMTR2ReToX4l1Au1VFLyVeBTFavzQnv5JxMFr3ukHVKpSrSA2MCk0lNJSykjUfz7Zg==", "_location": "/qrcode", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "qrcode@^1.5.3", "name": "qrcode", "escapedName": "qrcode", "rawSpec": "^1.5.3", "saveSpec": null, "fetchSpec": "^1.5.3"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/qrcode/-/qrcode-1.5.4.tgz", "_shasum": "5cb81d86eb57c675febb08cf007fff963405da88", "_spec": "qrcode@^1.5.3", "_where": "C:\\repos\\test", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bin": {"qrcode": "bin/qrcode"}, "browser": {"./lib/index.js": "./lib/browser.js", "fs": false}, "bugs": {"url": "https://github.com/soldair/node-qrcode/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"dijkstrajs": "^1.0.1", "pngjs": "^5.0.0", "yargs": "^15.3.1"}, "deprecated": false, "description": "QRCode / 2d Barcode api with both server side and client side support using canvas", "devDependencies": {"@babel/core": "^7.9.0", "@babel/preset-env": "^7.9.5", "@rollup/plugin-commonjs": "^11.1.0", "@rollup/plugin-node-resolve": "^7.1.3", "browserify": "^16.5.1", "canvas": "^2.8.0", "canvasutil": "0.0.4", "colors": "^1.4.0", "express": "^4.17.1", "htmlparser2": "^4.1.0", "rollup": "^2.6.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-terser": "^5.3.0", "sinon": "^9.0.2", "standard": "^16.0.4", "tap": "^16.2.0"}, "engines": {"node": ">=10.13.0"}, "files": ["bin", "build", "lib", "helper"], "homepage": "http://github.com/soldair/node-qrcode", "keywords": ["qr", "code", "canvas", "qrcode"], "license": "MIT", "main": "./lib/index.js", "name": "qrcode", "repository": {"type": "git", "url": "git://github.com/soldair/node-qrcode.git"}, "scripts": {"browser": "node examples/clientsideserver.js", "build": "rollup -c", "lint": "standard", "prepublish": "npm run build", "pretest": "npm run lint", "test": "node --throw-deprecation test.js"}, "standard": {"ignore": ["build/", "examples/vendors/", "lib/core/regex.js"]}, "version": "1.5.4"}